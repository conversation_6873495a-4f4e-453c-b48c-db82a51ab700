"""
知识图谱分析器 (增强版)
分析三元组结构，统计实体类型，特别关注功能和形态类别
新增功能：地块功能、建筑物详情、土地利用、密度分析等
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import re
from datetime import datetime
import networkx as nx
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class KnowledgeGraphAnalyzer:
    def __init__(self, kg_file_path, output_dir="./kg_analysis_results"):
        """
        初始化知识图谱分析器
        
        Args:
            kg_file_path: 知识图谱文件路径（三元组格式）
            output_dir: 分析结果输出目录
        """
        self.kg_file_path = kg_file_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 存储分析结果
        self.triples = []
        self.entities = set()
        self.relations = set()
        self.entity_types = defaultdict(set)
        self.relation_stats = defaultdict(int)
        self.entity_stats = defaultdict(int)
        
        # 实体类型模式 (增强版)
        self.entity_patterns = {
            'Region': r'^Region_',
            'Land': r'^Land_',
            'Building': r'^Building_',
            'POI': r'^POI_',
            'BusinessCircle': r'^BC_',
            'Category': r'^Cate_',
            'Function': r'^Func_',
            'Morphology': r'^Morph_',
            'LandUse': r'^LandUse_',  # ⭐️新增土地利用
            'Density': r'^Density_',  # ⭐️新增密度类型
            'Height': r'^Height_',    # ⭐️新增高度类型
            'Age': r'^Age_',          # ⭐️新增年代类型
            'Service': r'^Service_',  # ⭐️新增服务类型
            'Transport': r'^Transport_' # ⭐️新增交通类型
        }
        
        # 功能类型映射
        self.function_types = {
            'Func_Residential': '住宅',
            'Func_Commercial': '商业',
            'Func_Office': '办公',
            'Func_Industrial': '工业',
            'Func_Public': '公共',
            'Func_Education': '教育',
            'Func_Medical': '医疗',
            'Func_Cultural': '文化',
            'Func_Sports': '体育',
            'Func_Transport': '交通',
            'Func_Other': '其他'
        }
        
        # 形态类型映射
        self.morphology_types = {
            'Morph_LowRiseLowDensity': '低层低密度',
            'Morph_LowRiseMidDensity': '低层中密度',
            'Morph_LowRiseHighDensity': '低层高密度',
            'Morph_MidRiseLowDensity': '中层低密度',
            'Morph_MidRiseMidDensity': '中层中密度',
            'Morph_MidRiseHighDensity': '中层高密度',
            'Morph_HighRiseLowDensity': '高层低密度',
            'Morph_HighRiseMidDensity': '高层中密度',
            'Morph_HighRiseHighDensity': '高层高密度',
            'Morph_Vacant': '空地'
        }

        # 土地利用类型映射 ⭐️新增
        self.landuse_types = {
            'LandUse_Residential': '居住用地',
            'LandUse_Commercial': '商业用地',
            'LandUse_Industrial': '工业用地',
            'LandUse_Public': '公共设施用地',
            'LandUse_Green': '绿地',
            'LandUse_Transport': '交通用地',
            'LandUse_Water': '水域',
            'LandUse_Agricultural': '农业用地',
            'LandUse_Mixed': '混合用地',
            'LandUse_Other': '其他用地'
        }

        # POI类别映射 ⭐️新增
        self.poi_categories = {
            'Cate_Food': '餐饮服务',
            'Cate_Shopping': '购物消费',
            'Cate_Life': '生活服务',
            'Cate_Entertainment': '休闲娱乐',
            'Cate_Education': '教育培训',
            'Cate_Medical': '医疗保健',
            'Cate_Transport': '交通设施',
            'Cate_Finance': '金融保险',
            'Cate_Government': '政府机构',
            'Cate_Tourism': '旅游景点',
            'Cate_Sports': '体育健身',
            'Cate_Culture': '文化场所'
        }

        # 关系类型分组 ⭐️新增
        self.relation_groups = {
            '空间关系': ['borderBy', 'nearBy', 'connectedTo', 'locateAt', 'withinRegion', 'adjacentToLand'],
            '功能关系': ['similarFunction', 'hasFunctionBuilding', 'hasLandUse', 'cateOf'],
            '形态关系': ['hasMorphology', 'morphologySimilar'],
            '服务关系': ['provideService', 'belongTo'],
            '移动关系': ['flowTransition', 'densityInfluences'],
            '归属关系': ['belongsToBuilding', 'belongsToLand'],
            '便利关系': ['highConvenience']
        }
        
    def load_knowledge_graph(self):
        """加载知识图谱三元组"""
        print("🔍 加载知识图谱三元组...")
        
        try:
            with open(self.kg_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    parts = line.split('\t')
                    if len(parts) != 3:
                        print(f"⚠️ 第{line_num}行格式错误: {line}")
                        continue
                    
                    head, relation, tail = parts
                    self.triples.append((head, relation, tail))
                    self.entities.add(head)
                    self.entities.add(tail)
                    self.relations.add(relation)
                    
            print(f"✅ 成功加载 {len(self.triples):,} 个三元组")
            print(f"   实体数量: {len(self.entities):,}")
            print(f"   关系数量: {len(self.relations):,}")
            
        except FileNotFoundError:
            print(f"❌ 文件未找到: {self.kg_file_path}")
            return False
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
            
        return True
    
    def classify_entities(self):
        """对实体进行分类"""
        print("\n📊 分类实体类型...")
        
        for entity in self.entities:
            classified = False
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, entity):
                    self.entity_types[entity_type].add(entity)
                    self.entity_stats[entity_type] += 1
                    classified = True
                    break
            
            if not classified:
                self.entity_types['Unknown'].add(entity)
                self.entity_stats['Unknown'] += 1
        
        # 打印实体类型统计
        print("\n实体类型统计:")
        for entity_type, count in sorted(self.entity_stats.items(), 
                                       key=lambda x: x[1], reverse=True):
            percentage = count / len(self.entities) * 100
            print(f"  {entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)")
    
    def analyze_relations(self):
        """分析关系统计"""
        print("\n🔗 分析关系统计...")
        
        # 统计关系频次
        for head, relation, tail in self.triples:
            self.relation_stats[relation] += 1
        
        # 按频次排序
        sorted_relations = sorted(self.relation_stats.items(), 
                                key=lambda x: x[1], reverse=True)
        
        print("\n关系类型统计:")
        for relation, count in sorted_relations:
            percentage = count / len(self.triples) * 100
            print(f"  {relation:<25}: {count:>7,} ({percentage:>5.1f}%)")
        
        return sorted_relations
    
    def analyze_tail_entities(self):
        """分析尾实体类型分布"""
        print("\n🎯 分析尾实体类型分布...")
        
        tail_entity_stats = defaultdict(lambda: defaultdict(int))
        tail_by_relation = defaultdict(lambda: defaultdict(int))
        
        for head, relation, tail in self.triples:
            # 确定尾实体类型
            tail_type = 'Unknown'
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, tail):
                    tail_type = entity_type
                    break
            
            tail_entity_stats[tail_type][tail] += 1
            tail_by_relation[relation][tail_type] += 1
        
        # 打印尾实体类型统计
        print("\n尾实体类型分布:")
        for entity_type, entities in tail_entity_stats.items():
            unique_count = len(entities)
            total_count = sum(entities.values())
            print(f"  {entity_type:<15}: {unique_count:>6} 种 (出现 {total_count:>7,} 次)")
        
        # 按关系分析尾实体
        print("\n各关系的尾实体类型:")
        for relation in sorted(tail_by_relation.keys()):
            print(f"\n  📌 {relation}:")
            for tail_type, count in sorted(tail_by_relation[relation].items(), 
                                         key=lambda x: x[1], reverse=True):
                percentage = count / self.relation_stats[relation] * 100
                print(f"    → {tail_type:<15}: {count:>6,} ({percentage:>5.1f}%)")
        
        return tail_entity_stats, tail_by_relation
    
    def analyze_function_morphology_details(self):
        """详细分析功能和形态类别"""
        print("\n🏗️ 详细分析功能和形态类别...")
        
        # 功能类别详细分析
        function_entities = self.entity_types.get('Function', set())
        morphology_entities = self.entity_types.get('Morphology', set())
        
        print(f"\n功能类别详情 (共 {len(function_entities)} 种):")
        if function_entities:
            function_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in function_entities:
                    function_usage[tail] += 1
            
            # 按使用频次排序
            sorted_functions = sorted(function_usage.items(), 
                                    key=lambda x: x[1], reverse=True)
            
            for func_id, count in sorted_functions:
                func_name = self.function_types.get(func_id, func_id)
                percentage = count / sum(function_usage.values()) * 100 if function_usage.values() else 0
                print(f"  {func_id:<25} ({func_name:<8}): {count:>5} 次 ({percentage:>5.1f}%)")
        else:
            print("  未发现功能类别实体")
        
        print(f"\n形态类别详情 (共 {len(morphology_entities)} 种):")
        if morphology_entities:
            morphology_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in morphology_entities:
                    morphology_usage[tail] += 1
            
            # 按使用频次排序
            sorted_morphologies = sorted(morphology_usage.items(), 
                                       key=lambda x: x[1], reverse=True)
            
            for morph_id, count in sorted_morphologies:
                morph_name = self.morphology_types.get(morph_id, morph_id)
                percentage = count / sum(morphology_usage.values()) * 100 if morphology_usage.values() else 0
                print(f"  {morph_id:<30} ({morph_name:<12}): {count:>5} 次 ({percentage:>5.1f}%)")
        else:
            print("  未发现形态类别实体")
        
        return function_entities, morphology_entities

    def analyze_landuse_categories(self):
        """分析土地利用类别详情 ⭐️新增"""
        print("\n🗺️ 分析土地利用类别详情...")

        landuse_entities = self.entity_types.get('LandUse', set())

        if not landuse_entities:
            print("  未发现土地利用类别实体")
            return None

        print(f"\n土地利用类别详情 (共 {len(landuse_entities)} 种):")

        # 统计土地利用使用频次
        landuse_usage = defaultdict(int)
        landuse_relations = defaultdict(list)

        for head, relation, tail in self.triples:
            if tail in landuse_entities:
                landuse_usage[tail] += 1
                landuse_relations[tail].append((head, relation))

        # 按使用频次排序
        sorted_landuses = sorted(landuse_usage.items(), key=lambda x: x[1], reverse=True)

        for landuse_id, count in sorted_landuses:
            landuse_name = self.landuse_types.get(landuse_id, landuse_id)
            percentage = count / sum(landuse_usage.values()) * 100 if landuse_usage.values() else 0

            # 统计关联的地块数量
            related_lands = set()
            for head, relation in landuse_relations[landuse_id]:
                if head.startswith('Land_'):
                    related_lands.add(head)

            print(f"  {landuse_id:<25} ({landuse_name:<12}): {count:>5} 次, {len(related_lands):>4} 地块 ({percentage:>5.1f}%)")

        return landuse_entities, landuse_usage

    def analyze_poi_categories(self):
        """分析POI类别详情 ⭐️新增"""
        print("\n🏪 分析POI类别详情...")

        category_entities = self.entity_types.get('Category', set())
        poi_entities = self.entity_types.get('POI', set())

        if not category_entities:
            print("  未发现POI类别实体")
            return None

        print(f"\nPOI类别详情 (共 {len(category_entities)} 种):")

        # 统计POI类别使用频次和关联POI数量
        category_usage = defaultdict(int)
        category_pois = defaultdict(set)

        for head, relation, tail in self.triples:
            if tail in category_entities:
                category_usage[tail] += 1
                if head.startswith('POI_'):
                    category_pois[tail].add(head)

        # 按使用频次排序
        sorted_categories = sorted(category_usage.items(), key=lambda x: x[1], reverse=True)

        for category_id, count in sorted_categories:
            category_name = self.poi_categories.get(category_id, category_id)
            percentage = count / sum(category_usage.values()) * 100 if category_usage.values() else 0
            poi_count = len(category_pois[category_id])

            print(f"  {category_id:<20} ({category_name:<12}): {count:>5} 次, {poi_count:>5} POI ({percentage:>5.1f}%)")

        return category_entities, category_usage

    def analyze_building_details(self):
        """分析建筑物详情 ⭐️新增"""
        print("\n🏢 分析建筑物详情...")

        building_entities = self.entity_types.get('Building', set())

        if not building_entities:
            print("  未发现建筑物实体")
            return None

        print(f"\n建筑物详情分析 (共 {len(building_entities)} 个建筑):")

        # 分析建筑物的功能分布
        building_functions = defaultdict(int)
        building_morphologies = defaultdict(int)
        building_lands = defaultdict(set)
        building_regions = defaultdict(set)

        for head, relation, tail in self.triples:
            if head in building_entities:
                if relation == 'hasFunctionBuilding' and tail.startswith('Func_'):
                    building_functions[tail] += 1
                elif relation == 'belongsToLand' and tail.startswith('Land_'):
                    building_lands[head].add(tail)
                elif relation == 'withinRegion' and tail.startswith('Region_'):
                    building_regions[head].add(tail)

            # 分析地块的形态分布（间接关联建筑物）
            if head.startswith('Land_') and relation == 'hasMorphology' and tail.startswith('Morph_'):
                # 找到属于这个地块的建筑物
                for b_head, b_relation, b_tail in self.triples:
                    if b_relation == 'belongsToLand' and b_tail == head and b_head in building_entities:
                        building_morphologies[tail] += 1

        # 建筑功能分布
        print("\n  建筑功能分布:")
        if building_functions:
            sorted_functions = sorted(building_functions.items(), key=lambda x: x[1], reverse=True)
            for func_id, count in sorted_functions:
                func_name = self.function_types.get(func_id, func_id)
                percentage = count / sum(building_functions.values()) * 100
                print(f"    {func_id:<25} ({func_name:<8}): {count:>5} 建筑 ({percentage:>5.1f}%)")
        else:
            print("    未发现建筑功能关系")

        # 建筑形态分布（通过地块间接关联）
        print("\n  建筑形态分布 (通过地块关联):")
        if building_morphologies:
            sorted_morphologies = sorted(building_morphologies.items(), key=lambda x: x[1], reverse=True)
            for morph_id, count in sorted_morphologies:
                morph_name = self.morphology_types.get(morph_id, morph_id)
                percentage = count / sum(building_morphologies.values()) * 100
                print(f"    {morph_id:<30} ({morph_name:<12}): {count:>5} 建筑 ({percentage:>5.1f}%)")
        else:
            print("    未发现建筑形态关系")

        # 建筑空间分布
        print(f"\n  建筑空间分布:")
        print(f"    关联地块数: {len(set().union(*building_lands.values()))} 个")
        print(f"    关联区域数: {len(set().union(*building_regions.values()))} 个")
        print(f"    平均每地块建筑数: {len(building_entities) / len(set().union(*building_lands.values())):.1f} 个" if building_lands else "    无地块关联数据")

        return building_entities, building_functions, building_morphologies

    def analyze_relation_patterns(self):
        """分析关系模式 ⭐️新增"""
        print("\n🔗 分析关系模式...")

        # 按关系分组统计
        group_stats = defaultdict(int)
        relation_entity_pairs = defaultdict(lambda: defaultdict(int))

        for head, relation, tail in self.triples:
            # 确定关系所属分组
            relation_group = None
            for group_name, relations in self.relation_groups.items():
                if relation in relations:
                    relation_group = group_name
                    break

            if relation_group:
                group_stats[relation_group] += 1
            else:
                group_stats['其他关系'] += 1

            # 统计实体类型对
            head_type = self._get_entity_type(head)
            tail_type = self._get_entity_type(tail)
            relation_entity_pairs[relation][f"{head_type}->{tail_type}"] += 1

        # 打印关系分组统计
        print("\n关系分组统计:")
        total_relations = sum(group_stats.values())
        for group_name, count in sorted(group_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_relations * 100
            print(f"  {group_name:<12}: {count:>7,} ({percentage:>5.1f}%)")

        # 分析主要关系的实体类型模式
        print("\n主要关系的实体类型模式:")
        sorted_relations = sorted(self.relation_stats.items(), key=lambda x: x[1], reverse=True)[:10]

        for relation, count in sorted_relations:
            print(f"\n  📌 {relation} ({count:,} 次):")
            entity_pairs = relation_entity_pairs[relation]
            sorted_pairs = sorted(entity_pairs.items(), key=lambda x: x[1], reverse=True)[:5]

            for pair, pair_count in sorted_pairs:
                percentage = pair_count / count * 100
                print(f"    {pair:<20}: {pair_count:>6,} ({percentage:>5.1f}%)")

        return group_stats, relation_entity_pairs

    def _get_entity_type(self, entity):
        """获取实体类型"""
        for entity_type, pattern in self.entity_patterns.items():
            if re.match(pattern, entity):
                return entity_type
        return 'Unknown'

    def analyze_triple_patterns(self):
        """分析所有三元组的种类和模式 ⭐️新增"""
        print("\n🔍 分析所有三元组的种类和模式...")

        # 统计三元组模式 (头实体类型-关系-尾实体类型)
        triple_patterns = defaultdict(int)
        triple_examples = defaultdict(list)
        relation_patterns = defaultdict(lambda: defaultdict(int))

        for head, relation, tail in self.triples:
            head_type = self._get_entity_type(head)
            tail_type = self._get_entity_type(tail)

            # 三元组模式
            pattern = f"{head_type} --{relation}--> {tail_type}"
            triple_patterns[pattern] += 1

            # 保存示例（最多保存3个）
            if len(triple_examples[pattern]) < 3:
                triple_examples[pattern].append((head, relation, tail))

            # 关系的实体类型模式
            relation_patterns[relation][f"{head_type}->{tail_type}"] += 1

        # 按频次排序显示三元组模式
        print(f"\n📋 三元组模式统计 (共 {len(triple_patterns)} 种模式):")
        print("="*80)

        sorted_patterns = sorted(triple_patterns.items(), key=lambda x: x[1], reverse=True)

        for i, (pattern, count) in enumerate(sorted_patterns, 1):
            percentage = count / len(self.triples) * 100
            print(f"\n{i:2d}. {pattern}")
            print(f"    数量: {count:,} 个 ({percentage:.2f}%)")

            # 显示示例
            examples = triple_examples[pattern]
            if examples:
                print(f"    示例:")
                for j, (h, r, t) in enumerate(examples, 1):
                    # 截断过长的实体名
                    h_short = h[:20] + "..." if len(h) > 20 else h
                    t_short = t[:20] + "..." if len(t) > 20 else t
                    print(f"      {j}. ({h_short}, {r}, {t_short})")

        # 按关系分析实体类型模式
        print(f"\n🔗 各关系的实体类型模式:")
        print("="*80)

        for relation in sorted(self.relations):
            patterns = relation_patterns[relation]
            total_count = sum(patterns.values())

            print(f"\n📌 {relation} (总计: {total_count:,} 个)")

            # 按频次排序显示模式
            sorted_rel_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
            for pattern, count in sorted_rel_patterns:
                percentage = count / total_count * 100
                print(f"    {pattern:<25}: {count:>6,} ({percentage:>5.1f}%)")

        return triple_patterns, relation_patterns

    def analyze_entity_type_interactions(self):
        """分析实体类型间的交互模式 ⭐️新增"""
        print("\n🌐 分析实体类型间的交互模式...")

        # 统计实体类型间的连接
        type_interactions = defaultdict(lambda: defaultdict(int))
        type_relations = defaultdict(lambda: defaultdict(set))

        for head, relation, tail in self.triples:
            head_type = self._get_entity_type(head)
            tail_type = self._get_entity_type(tail)

            # 统计类型间连接
            type_interactions[head_type][tail_type] += 1
            type_relations[head_type][tail_type].add(relation)

        # 创建交互矩阵
        all_types = sorted(set(self.entity_stats.keys()))

        print(f"\n📊 实体类型交互矩阵:")
        print("="*80)

        # 打印表头
        print(f"{'源类型':<15}", end="")
        for target_type in all_types:
            print(f"{target_type[:8]:>10}", end="")
        print()

        print("-" * (15 + 10 * len(all_types)))

        # 打印矩阵内容
        for source_type in all_types:
            print(f"{source_type:<15}", end="")
            for target_type in all_types:
                count = type_interactions[source_type][target_type]
                if count > 0:
                    print(f"{count:>10,}", end="")
                else:
                    print(f"{'':>10}", end="")
            print()

        # 详细分析主要交互
        print(f"\n🔍 主要实体类型交互详情:")
        print("="*80)

        # 找出交互最频繁的类型对
        all_interactions = []
        for source_type, targets in type_interactions.items():
            for target_type, count in targets.items():
                if count > 0:
                    relations = type_relations[source_type][target_type]
                    all_interactions.append((source_type, target_type, count, relations))

        # 按交互频次排序
        all_interactions.sort(key=lambda x: x[2], reverse=True)

        # 显示前20个最频繁的交互
        for i, (source, target, count, relations) in enumerate(all_interactions[:20], 1):
            percentage = count / len(self.triples) * 100
            relations_str = ", ".join(sorted(relations))

            print(f"\n{i:2d}. {source} → {target}")
            print(f"    连接数: {count:,} ({percentage:.2f}%)")
            print(f"    关系类型: {relations_str}")

        return type_interactions, type_relations

    def generate_comprehensive_triple_report(self):
        """生成全面的三元组分析报告 ⭐️新增"""
        print("\n📝 生成全面的三元组分析报告...")

        report_filename = f"comprehensive_triple_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        with open(self.output_dir / report_filename, 'w', encoding='utf-8') as f:
            f.write("="*100 + "\n")
            f.write("知识图谱三元组全面分析报告\n")
            f.write("="*100 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据文件: {self.kg_file_path}\n\n")

            # 1. 基本统计
            f.write("1. 基本统计信息\n")
            f.write("-"*50 + "\n")
            f.write(f"三元组总数: {len(self.triples):,}\n")
            f.write(f"实体总数: {len(self.entities):,}\n")
            f.write(f"关系类型数: {len(self.relations):,}\n")
            f.write(f"实体类型数: {len(self.entity_stats):,}\n\n")

            # 2. 所有关系类型列表
            f.write("2. 所有关系类型列表\n")
            f.write("-"*50 + "\n")
            for i, relation in enumerate(sorted(self.relations), 1):
                count = self.relation_stats[relation]
                percentage = count / len(self.triples) * 100
                f.write(f"{i:2d}. {relation:<25}: {count:>7,} ({percentage:>5.1f}%)\n")
            f.write("\n")

            # 3. 所有实体类型列表
            f.write("3. 所有实体类型列表\n")
            f.write("-"*50 + "\n")
            for i, (entity_type, count) in enumerate(sorted(self.entity_stats.items(), key=lambda x: x[1], reverse=True), 1):
                percentage = count / len(self.entities) * 100
                f.write(f"{i:2d}. {entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)\n")
            f.write("\n")

            # 4. 三元组模式分析
            triple_patterns, _ = self.analyze_triple_patterns()
            f.write("4. 三元组模式分析\n")
            f.write("-"*50 + "\n")
            f.write(f"发现 {len(triple_patterns)} 种不同的三元组模式:\n\n")

            sorted_patterns = sorted(triple_patterns.items(), key=lambda x: x[1], reverse=True)
            for i, (pattern, count) in enumerate(sorted_patterns, 1):
                percentage = count / len(self.triples) * 100
                f.write(f"{i:2d}. {pattern}\n")
                f.write(f"    数量: {count:,} ({percentage:.2f}%)\n\n")

            # 5. 关系分组统计
            f.write("5. 关系分组统计\n")
            f.write("-"*50 + "\n")
            group_stats, _ = self.analyze_relation_patterns()
            for group_name, count in sorted(group_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / len(self.triples) * 100
                f.write(f"{group_name:<12}: {count:>7,} ({percentage:>5.1f}%)\n")
            f.write("\n")

            # 6. 实体类型交互分析
            type_interactions, _ = self.analyze_entity_type_interactions()
            f.write("6. 实体类型交互统计\n")
            f.write("-"*50 + "\n")

            all_interactions = []
            for source_type, targets in type_interactions.items():
                for target_type, count in targets.items():
                    if count > 0:
                        all_interactions.append((source_type, target_type, count))

            all_interactions.sort(key=lambda x: x[2], reverse=True)

            for i, (source, target, count) in enumerate(all_interactions[:30], 1):
                percentage = count / len(self.triples) * 100
                f.write(f"{i:2d}. {source} → {target}: {count:,} ({percentage:.2f}%)\n")

        print(f"✅ 全面三元组分析报告已保存: {self.output_dir / report_filename}")
        return report_filename

    def analyze_connectivity_metrics(self):
        """分析连通性指标 ⭐️新增"""
        print("\n📊 分析连通性指标...")

        # 构建实体连接统计
        entity_connections = defaultdict(int)
        entity_in_degree = defaultdict(int)
        entity_out_degree = defaultdict(int)

        for head, relation, tail in self.triples:
            entity_connections[head] += 1
            entity_connections[tail] += 1
            entity_out_degree[head] += 1
            entity_in_degree[tail] += 1

        # 计算连通性指标
        total_entities = len(self.entities)
        total_connections = sum(entity_connections.values()) // 2  # 每条边被计算两次
        avg_degree = sum(entity_connections.values()) / total_entities if total_entities > 0 else 0

        # 找出连接度最高的实体
        top_connected = sorted(entity_connections.items(), key=lambda x: x[1], reverse=True)[:10]

        print(f"\n连通性统计:")
        print(f"  总实体数: {total_entities:,}")
        print(f"  总连接数: {total_connections:,}")
        print(f"  平均连接度: {avg_degree:.2f}")
        print(f"  图密度: {(2 * total_connections) / (total_entities * (total_entities - 1)):.6f}" if total_entities > 1 else "  图密度: 0")

        print(f"\n连接度最高的实体 (Top 10):")
        for entity, degree in top_connected:
            entity_type = self._get_entity_type(entity)
            print(f"  {entity:<25} ({entity_type:<12}): {degree:>4} 连接")

        # 按实体类型分析连通性
        print(f"\n各实体类型平均连接度:")
        type_connections = defaultdict(list)
        for entity, degree in entity_connections.items():
            entity_type = self._get_entity_type(entity)
            type_connections[entity_type].append(degree)

        for entity_type, degrees in sorted(type_connections.items()):
            avg_degree_type = np.mean(degrees)
            max_degree_type = max(degrees)
            min_degree_type = min(degrees)
            print(f"  {entity_type:<15}: 平均 {avg_degree_type:>6.1f}, 最大 {max_degree_type:>4}, 最小 {min_degree_type:>4}")

        return entity_connections, type_connections

    def generate_sample_triples(self, sample_size=50):
        """生成三元组样本展示"""
        print(f"\n📋 三元组样本展示 (前 {sample_size} 个):")
        
        sample_triples = self.triples[:sample_size]
        
        print("\n头实体\t\t\t关系\t\t\t尾实体")
        print("-" * 80)
        
        for i, (head, relation, tail) in enumerate(sample_triples, 1):
            # 截断过长的实体名
            head_display = head[:20] + "..." if len(head) > 20 else head
            tail_display = tail[:20] + "..." if len(tail) > 20 else tail
            
            print(f"{head_display:<25}\t{relation:<20}\t{tail_display}")
            
            # 每10行显示一个分隔符
            if i % 10 == 0 and i < sample_size:
                print("-" * 80)
        
        return sample_triples
    
    def create_visualizations(self):
        """创建增强版可视化图表 ⭐️增强"""
        print("\n📈 生成增强版可视化图表...")

        # 创建更大的图表布局
        fig = plt.figure(figsize=(20, 16))

        # 1. 实体类型分布饼图
        plt.subplot(3, 3, 1)
        entity_counts = list(self.entity_stats.values())
        entity_labels = list(self.entity_stats.keys())
        colors = plt.cm.Set3(np.linspace(0, 1, len(entity_labels)))

        plt.pie(entity_counts, labels=entity_labels, autopct='%1.1f%%',
                colors=colors, startangle=90)
        plt.title('实体类型分布', fontsize=12, fontweight='bold')

        # 2. 关系类型频次柱状图
        plt.subplot(3, 3, 2)
        relations = list(self.relation_stats.keys())[:10]  # 前10个关系
        counts = [self.relation_stats[r] for r in relations]

        plt.bar(range(len(relations)), counts, color='skyblue', alpha=0.7)
        plt.xticks(range(len(relations)), relations, rotation=45, ha='right')
        plt.ylabel('频次')
        plt.title('关系类型频次 (Top 10)', fontsize=12, fontweight='bold')

        # 3. 功能类别分布
        plt.subplot(3, 3, 3)
        function_entities = self.entity_types.get('Function', set())
        if function_entities:
            function_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in function_entities:
                    function_usage[tail] += 1

            if function_usage:
                func_names = [self.function_types.get(f, f.replace('Func_', ''))
                             for f in function_usage.keys()]
                func_counts = list(function_usage.values())

                plt.bar(range(len(func_names)), func_counts, color='lightgreen', alpha=0.7)
                plt.xticks(range(len(func_names)), func_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('建筑功能类别分布', fontsize=12, fontweight='bold')

        # 4. 形态类别分布
        plt.subplot(3, 3, 4)
        morphology_entities = self.entity_types.get('Morphology', set())
        if morphology_entities:
            morphology_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in morphology_entities:
                    morphology_usage[tail] += 1

            if morphology_usage:
                morph_names = [self.morphology_types.get(m, m.replace('Morph_', ''))
                              for m in morphology_usage.keys()]
                morph_counts = list(morphology_usage.values())

                plt.bar(range(len(morph_names)), morph_counts, color='lightcoral', alpha=0.7)
                plt.xticks(range(len(morph_names)), morph_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('建筑形态类别分布', fontsize=12, fontweight='bold')

        # 5. 土地利用类别分布 ⭐️新增
        plt.subplot(3, 3, 5)
        landuse_entities = self.entity_types.get('LandUse', set())
        if landuse_entities:
            landuse_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in landuse_entities:
                    landuse_usage[tail] += 1

            if landuse_usage:
                landuse_names = [self.landuse_types.get(l, l.replace('LandUse_', ''))
                               for l in landuse_usage.keys()]
                landuse_counts = list(landuse_usage.values())

                plt.bar(range(len(landuse_names)), landuse_counts, color='orange', alpha=0.7)
                plt.xticks(range(len(landuse_names)), landuse_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('土地利用类别分布', fontsize=12, fontweight='bold')
        else:
            plt.text(0.5, 0.5, '无土地利用数据', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title('土地利用类别分布', fontsize=12, fontweight='bold')

        # 6. POI类别分布 ⭐️新增
        plt.subplot(3, 3, 6)
        category_entities = self.entity_types.get('Category', set())
        if category_entities:
            category_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in category_entities:
                    category_usage[tail] += 1

            if category_usage:
                # 只显示前8个类别
                sorted_categories = sorted(category_usage.items(), key=lambda x: x[1], reverse=True)[:8]
                category_names = [self.poi_categories.get(c[0], c[0].replace('Cate_', ''))
                                for c in sorted_categories]
                category_counts = [c[1] for c in sorted_categories]

                plt.bar(range(len(category_names)), category_counts, color='purple', alpha=0.7)
                plt.xticks(range(len(category_names)), category_names, rotation=45, ha='right')
                plt.ylabel('使用次数')
                plt.title('POI类别分布 (Top 8)', fontsize=12, fontweight='bold')
        else:
            plt.text(0.5, 0.5, '无POI类别数据', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title('POI类别分布', fontsize=12, fontweight='bold')

        # 7. 关系分组统计 ⭐️新增
        plt.subplot(3, 3, 7)
        group_stats = defaultdict(int)
        for head, relation, tail in self.triples:
            relation_group = None
            for group_name, relations in self.relation_groups.items():
                if relation in relations:
                    relation_group = group_name
                    break
            if relation_group:
                group_stats[relation_group] += 1
            else:
                group_stats['其他关系'] += 1

        if group_stats:
            group_names = list(group_stats.keys())
            group_counts = list(group_stats.values())
            colors = plt.cm.Pastel1(np.linspace(0, 1, len(group_names)))

            plt.pie(group_counts, labels=group_names, autopct='%1.1f%%',
                   colors=colors, startangle=90)
            plt.title('关系分组分布', fontsize=12, fontweight='bold')

        # 8. 连通性分析 ⭐️新增
        plt.subplot(3, 3, 8)
        entity_connections = defaultdict(int)
        for head, relation, tail in self.triples:
            entity_connections[head] += 1
            entity_connections[tail] += 1

        # 按实体类型统计平均连接度
        type_connections = defaultdict(list)
        for entity, degree in entity_connections.items():
            entity_type = self._get_entity_type(entity)
            type_connections[entity_type].append(degree)

        type_avg_degrees = {}
        for entity_type, degrees in type_connections.items():
            type_avg_degrees[entity_type] = np.mean(degrees)

        if type_avg_degrees:
            types = list(type_avg_degrees.keys())
            avg_degrees = list(type_avg_degrees.values())

            plt.bar(range(len(types)), avg_degrees, color='teal', alpha=0.7)
            plt.xticks(range(len(types)), types, rotation=45, ha='right')
            plt.ylabel('平均连接度')
            plt.title('各实体类型平均连接度', fontsize=12, fontweight='bold')

        # 9. 实体数量对比 ⭐️新增
        plt.subplot(3, 3, 9)
        # 显示主要实体类型的数量对比
        main_types = ['Region', 'Land', 'Building', 'POI', 'Function', 'Morphology', 'LandUse', 'Category']
        main_counts = [self.entity_stats.get(t, 0) for t in main_types]

        plt.bar(range(len(main_types)), main_counts, color='gold', alpha=0.7)
        plt.xticks(range(len(main_types)), main_types, rotation=45, ha='right')
        plt.ylabel('实体数量')
        plt.title('主要实体类型数量对比', fontsize=12, fontweight='bold')

        # 添加数值标签
        for i, count in enumerate(main_counts):
            if count > 0:
                plt.text(i, count + max(main_counts) * 0.01, str(count),
                        ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图表
        viz_path = self.output_dir / "enhanced_kg_analysis_visualization.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        print(f"✅ 增强版可视化图表已保存: {viz_path}")

        plt.show()
    
    def export_analysis_results(self):
        """导出分析结果"""
        print("\n💾 导出分析结果...")
        
        # 1. 基本统计信息
        basic_stats = {
            'total_triples': len(self.triples),
            'total_entities': len(self.entities),
            'total_relations': len(self.relations),
            'entity_type_counts': dict(self.entity_stats),
            'relation_counts': dict(self.relation_stats)
        }
        
        with open(self.output_dir / "basic_statistics.json", 'w', encoding='utf-8') as f:
            json.dump(basic_stats, f, ensure_ascii=False, indent=2)
        
        # 2. 实体类型详情
        entity_details = {}
        for entity_type, entities in self.entity_types.items():
            entity_details[entity_type] = {
                'count': len(entities),
                'entities': sorted(list(entities))[:100]  # 最多保存100个示例
            }
        
        with open(self.output_dir / "entity_details.json", 'w', encoding='utf-8') as f:
            json.dump(entity_details, f, ensure_ascii=False, indent=2)
        
        # 3. 功能和形态详情
        function_morphology_details = {
            'functions': {
                'mapping': self.function_types,
                'entities': sorted(list(self.entity_types.get('Function', set())))
            },
            'morphologies': {
                'mapping': self.morphology_types,
                'entities': sorted(list(self.entity_types.get('Morphology', set())))
            }
        }
        
        with open(self.output_dir / "function_morphology_details.json", 'w', encoding='utf-8') as f:
            json.dump(function_morphology_details, f, ensure_ascii=False, indent=2)
        
        # 4. 三元组样本
        sample_triples = self.triples[:1000]  # 前1000个三元组样本
        triples_df = pd.DataFrame(sample_triples, columns=['头实体', '关系', '尾实体'])
        triples_df.to_csv(self.output_dir / "sample_triples.csv", 
                         index=False, encoding='utf-8-sig')
        
        # 5. 尾实体统计
        tail_stats = defaultdict(lambda: defaultdict(int))
        for head, relation, tail in self.triples:
            tail_type = 'Unknown'
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, tail):
                    tail_type = entity_type
                    break
            tail_stats[relation][tail_type] += 1
        
        # 转换为DataFrame
        tail_stats_data = []
        for relation, type_counts in tail_stats.items():
            for tail_type, count in type_counts.items():
                tail_stats_data.append({
                    '关系': relation,
                    '尾实体类型': tail_type,
                    '数量': count,
                    '占该关系比例': f"{count/self.relation_stats[relation]*100:.1f}%"
                })
        
        tail_stats_df = pd.DataFrame(tail_stats_data)
        tail_stats_df.to_csv(self.output_dir / "tail_entity_statistics.csv", 
                           index=False, encoding='utf-8-sig')
        
        print(f"✅ 分析结果已导出到: {self.output_dir}")
        print("   📄 basic_statistics.json - 基本统计信息")
        print("   📄 entity_details.json - 实体类型详情")
        print("   📄 function_morphology_details.json - 功能形态详情")
        print("   📄 sample_triples.csv - 三元组样本")
        print("   📄 tail_entity_statistics.csv - 尾实体统计")

    def export_enhanced_analysis_results(self):
        """导出增强版分析结果 ⭐️新增"""
        print("💾 导出增强版分析结果...")

        # 调用原有的导出功能
        self.export_analysis_results()

        # 6. 土地利用详情
        landuse_entities = self.entity_types.get('LandUse', set())
        if landuse_entities:
            landuse_details = {
                'mapping': self.landuse_types,
                'entities': sorted(list(landuse_entities)),
                'usage_stats': {}
            }

            # 统计使用情况
            for head, relation, tail in self.triples:
                if tail in landuse_entities:
                    if tail not in landuse_details['usage_stats']:
                        landuse_details['usage_stats'][tail] = 0
                    landuse_details['usage_stats'][tail] += 1

            with open(self.output_dir / "landuse_details.json", 'w', encoding='utf-8') as f:
                json.dump(landuse_details, f, ensure_ascii=False, indent=2)

        # 7. POI类别详情
        category_entities = self.entity_types.get('Category', set())
        if category_entities:
            category_details = {
                'mapping': self.poi_categories,
                'entities': sorted(list(category_entities)),
                'usage_stats': {}
            }

            # 统计使用情况
            for head, relation, tail in self.triples:
                if tail in category_entities:
                    if tail not in category_details['usage_stats']:
                        category_details['usage_stats'][tail] = 0
                    category_details['usage_stats'][tail] += 1

            with open(self.output_dir / "poi_category_details.json", 'w', encoding='utf-8') as f:
                json.dump(category_details, f, ensure_ascii=False, indent=2)

        # 8. 建筑物详情统计
        building_entities = self.entity_types.get('Building', set())
        if building_entities:
            building_analysis = {
                'total_buildings': len(building_entities),
                'function_distribution': {},
                'spatial_distribution': {},
                'morphology_distribution': {}
            }

            # 分析建筑功能分布
            for head, relation, tail in self.triples:
                if head in building_entities and relation == 'hasFunctionBuilding':
                    if tail not in building_analysis['function_distribution']:
                        building_analysis['function_distribution'][tail] = 0
                    building_analysis['function_distribution'][tail] += 1

            with open(self.output_dir / "building_analysis.json", 'w', encoding='utf-8') as f:
                json.dump(building_analysis, f, ensure_ascii=False, indent=2)

        # 9. 关系模式分析
        relation_patterns = {
            'relation_groups': dict(self.relation_groups),
            'group_statistics': {},
            'entity_type_patterns': {}
        }

        # 统计关系分组
        group_stats = defaultdict(int)
        for head, relation, tail in self.triples:
            relation_group = None
            for group_name, relations in self.relation_groups.items():
                if relation in relations:
                    relation_group = group_name
                    break
            if relation_group:
                group_stats[relation_group] += 1
            else:
                group_stats['其他关系'] += 1

        relation_patterns['group_statistics'] = dict(group_stats)

        with open(self.output_dir / "relation_patterns.json", 'w', encoding='utf-8') as f:
            json.dump(relation_patterns, f, ensure_ascii=False, indent=2)

        # 10. 连通性分析
        entity_connections = defaultdict(int)
        for head, relation, tail in self.triples:
            entity_connections[head] += 1
            entity_connections[tail] += 1

        connectivity_analysis = {
            'total_entities': len(self.entities),
            'total_connections': sum(entity_connections.values()) // 2,
            'average_degree': sum(entity_connections.values()) / len(self.entities) if self.entities else 0,
            'top_connected_entities': dict(sorted(entity_connections.items(), key=lambda x: x[1], reverse=True)[:20]),
            'type_connectivity': {}
        }

        # 按类型统计连通性
        type_connections = defaultdict(list)
        for entity, degree in entity_connections.items():
            entity_type = self._get_entity_type(entity)
            type_connections[entity_type].append(degree)

        for entity_type, degrees in type_connections.items():
            connectivity_analysis['type_connectivity'][entity_type] = {
                'count': len(degrees),
                'avg_degree': np.mean(degrees),
                'max_degree': max(degrees),
                'min_degree': min(degrees)
            }

        with open(self.output_dir / "connectivity_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(connectivity_analysis, f, ensure_ascii=False, indent=2)

        print(f"✅ 增强版分析结果已导出到: {self.output_dir}")
        print("   📄 landuse_details.json - 土地利用详情")
        print("   📄 poi_category_details.json - POI类别详情")
        print("   📄 building_analysis.json - 建筑物分析")
        print("   📄 relation_patterns.json - 关系模式分析")
        print("   📄 connectivity_analysis.json - 连通性分析")

    def generate_analysis_report(self, start_time):
        """生成详细分析报告 ⭐️新增"""
        print("📝 生成详细分析报告...")

        report_filename = f"kg_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        with open(self.output_dir / report_filename, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("知识图谱分析报告 (增强版)\n")
            f.write("="*80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析耗时: {(datetime.now() - start_time).total_seconds():.1f} 秒\n\n")

            # 1. 基本统计
            f.write("1. 基本统计信息\n")
            f.write("-"*40 + "\n")
            f.write(f"三元组总数: {len(self.triples):,}\n")
            f.write(f"实体总数: {len(self.entities):,}\n")
            f.write(f"关系类型数: {len(self.relations):,}\n")
            f.write(f"实体类型数: {len(self.entity_stats):,}\n\n")

            # 2. 实体类型分布
            f.write("2. 实体类型分布\n")
            f.write("-"*40 + "\n")
            for entity_type, count in sorted(self.entity_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / len(self.entities) * 100
                f.write(f"{entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)\n")
            f.write("\n")

            # 3. 关系类型分布
            f.write("3. 关系类型分布 (前10)\n")
            f.write("-"*40 + "\n")
            sorted_relations = sorted(self.relation_stats.items(), key=lambda x: x[1], reverse=True)[:10]
            for relation, count in sorted_relations:
                percentage = count / len(self.triples) * 100
                f.write(f"{relation:<25}: {count:>7,} ({percentage:>5.1f}%)\n")
            f.write("\n")

            # 4. 功能类别详情
            function_entities = self.entity_types.get('Function', set())
            if function_entities:
                f.write("4. 建筑功能类别详情\n")
                f.write("-"*40 + "\n")
                function_usage = defaultdict(int)
                for head, relation, tail in self.triples:
                    if tail in function_entities:
                        function_usage[tail] += 1

                for func_id, count in sorted(function_usage.items(), key=lambda x: x[1], reverse=True):
                    func_name = self.function_types.get(func_id, func_id)
                    percentage = count / sum(function_usage.values()) * 100
                    f.write(f"{func_id:<25} ({func_name:<8}): {count:>5} 次 ({percentage:>5.1f}%)\n")
                f.write("\n")

            # 5. 土地利用类别详情
            landuse_entities = self.entity_types.get('LandUse', set())
            if landuse_entities:
                f.write("5. 土地利用类别详情\n")
                f.write("-"*40 + "\n")
                landuse_usage = defaultdict(int)
                for head, relation, tail in self.triples:
                    if tail in landuse_entities:
                        landuse_usage[tail] += 1

                for landuse_id, count in sorted(landuse_usage.items(), key=lambda x: x[1], reverse=True):
                    landuse_name = self.landuse_types.get(landuse_id, landuse_id)
                    percentage = count / sum(landuse_usage.values()) * 100
                    f.write(f"{landuse_id:<25} ({landuse_name:<12}): {count:>5} 次 ({percentage:>5.1f}%)\n")
                f.write("\n")

            # 6. 连通性分析
            f.write("6. 连通性分析\n")
            f.write("-"*40 + "\n")
            entity_connections = defaultdict(int)
            for head, relation, tail in self.triples:
                entity_connections[head] += 1
                entity_connections[tail] += 1

            total_connections = sum(entity_connections.values()) // 2
            avg_degree = sum(entity_connections.values()) / len(self.entities) if self.entities else 0

            f.write(f"总连接数: {total_connections:,}\n")
            f.write(f"平均连接度: {avg_degree:.2f}\n")
            f.write(f"图密度: {(2 * total_connections) / (len(self.entities) * (len(self.entities) - 1)):.6f}\n" if len(self.entities) > 1 else "图密度: 0\n")
            f.write("\n")

            # 7. 分析总结
            f.write("7. 分析总结\n")
            f.write("-"*40 + "\n")
            f.write("本次分析涵盖了以下方面：\n")
            f.write("- 实体类型分布和统计\n")
            f.write("- 关系类型分析和模式识别\n")
            f.write("- 建筑功能和形态类别详情\n")
            f.write("- 土地利用类别分析\n")
            f.write("- POI类别分布统计\n")
            f.write("- 建筑物详情分析\n")
            f.write("- 关系模式和分组分析\n")
            f.write("- 连通性指标计算\n")
            f.write("- 可视化图表生成\n")

        print(f"✅ 详细分析报告已保存: {self.output_dir / report_filename}")

    def run_full_analysis(self):
        """运行完整分析 (增强版)"""
        print("🚀 开始知识图谱完整分析 (增强版)")
        print("=" * 80)

        start_time = datetime.now()

        # 1. 加载数据
        if not self.load_knowledge_graph():
            return False

        # 2. 分类实体
        print("\n" + "="*50)
        self.classify_entities()

        # 3. 分析关系
        print("\n" + "="*50)
        self.analyze_relations()

        # 4. 分析尾实体
        print("\n" + "="*50)
        self.analyze_tail_entities()

        # 5. 详细分析功能和形态
        print("\n" + "="*50)
        self.analyze_function_morphology_details()

        # 6. 分析土地利用类别 ⭐️新增
        print("\n" + "="*50)
        self.analyze_landuse_categories()

        # 7. 分析POI类别 ⭐️新增
        print("\n" + "="*50)
        self.analyze_poi_categories()

        # 8. 分析建筑物详情 ⭐️新增
        print("\n" + "="*50)
        self.analyze_building_details()

        # 9. 分析关系模式 ⭐️新增
        print("\n" + "="*50)
        self.analyze_relation_patterns()

        # 10. 分析三元组模式 ⭐️新增
        print("\n" + "="*50)
        self.analyze_triple_patterns()

        # 11. 分析实体类型交互 ⭐️新增
        print("\n" + "="*50)
        self.analyze_entity_type_interactions()

        # 12. 分析连通性指标 ⭐️新增
        print("\n" + "="*50)
        self.analyze_connectivity_metrics()

        # 13. 生成样本展示
        print("\n" + "="*50)
        self.generate_sample_triples()

        # 14. 创建增强版可视化
        try:
            print("\n" + "="*50)
            self.create_visualizations()
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")

        # 15. 导出增强版结果
        print("\n" + "="*50)
        self.export_enhanced_analysis_results()

        # 16. 生成分析报告
        print("\n" + "="*50)
        self.generate_analysis_report(start_time)

        # 17. 生成全面三元组分析报告 ⭐️新增
        print("\n" + "="*50)
        self.generate_comprehensive_triple_report()

        print("\n" + "=" * 80)
        print("🎉 知识图谱增强版分析完成！")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"⏱️ 总耗时: {duration:.1f} 秒")

        return True


def main():
    """主函数示例 (增强版)"""
    # 配置文件路径
    kg_file_path = r"D:\研二\知识图谱\图例\形态分类\kg_with_building_fixed.txt"
    output_dir = r"D:\研二\知识图谱\图例\形态分类\enhanced_analysis_results"

    print("🚀 启动增强版知识图谱分析器")
    print("支持的分析功能:")
    print("  ✅ 基础实体和关系统计")
    print("  ✅ 建筑功能类别详情分析")
    print("  ✅ 建筑形态类别详情分析")
    print("  ✅ 土地利用类别详情分析 ⭐️")
    print("  ✅ POI类别详情分析 ⭐️")
    print("  ✅ 建筑物详情分析 ⭐️")
    print("  ✅ 关系模式和分组分析 ⭐️")
    print("  ✅ 三元组模式分析 ⭐️新增")
    print("  ✅ 实体类型交互分析 ⭐️新增")
    print("  ✅ 连通性指标分析 ⭐️")
    print("  ✅ 增强版可视化图表 (3×3布局) ⭐️")
    print("  ✅ 详细分析报告生成 ⭐️")
    print("  ✅ 全面三元组分析报告 ⭐️新增")

    # 创建分析器
    analyzer = KnowledgeGraphAnalyzer(kg_file_path, output_dir)

    # 运行增强版分析
    success = analyzer.run_full_analysis()

    if success:
        print("\n📋 增强版分析总结:")
        print(f"   三元组总数: {len(analyzer.triples):,}")
        print(f"   实体总数: {len(analyzer.entities):,}")
        print(f"   关系类型数: {len(analyzer.relations):,}")
        print(f"   实体类型数: {len(analyzer.entity_stats):,}")

        # 详细展示各类别数量
        function_count = len(analyzer.entity_types.get('Function', set()))
        morphology_count = len(analyzer.entity_types.get('Morphology', set()))
        landuse_count = len(analyzer.entity_types.get('LandUse', set()))
        category_count = len(analyzer.entity_types.get('Category', set()))
        building_count = len(analyzer.entity_types.get('Building', set()))

        print(f"\n🎯 详细类别统计:")
        print(f"   建筑功能类别: {function_count} 种")
        print(f"   建筑形态类别: {morphology_count} 种")
        print(f"   土地利用类别: {landuse_count} 种 ⭐️")
        print(f"   POI类别: {category_count} 种 ⭐️")
        print(f"   建筑物实体: {building_count:,} 个 ⭐️")

        # 展示具体类别
        if function_count > 0:
            print(f"\n   🏢 建筑功能类别:")
            for func_id in sorted(analyzer.entity_types['Function']):
                func_name = analyzer.function_types.get(func_id, func_id)
                print(f"     {func_id} ({func_name})")

        if landuse_count > 0:
            print(f"\n   🗺️ 土地利用类别:")
            for landuse_id in sorted(analyzer.entity_types['LandUse']):
                landuse_name = analyzer.landuse_types.get(landuse_id, landuse_id)
                print(f"     {landuse_id} ({landuse_name})")

        if morphology_count > 0:
            print(f"\n   🏗️ 建筑形态类别:")
            for morph_id in sorted(analyzer.entity_types['Morphology']):
                morph_name = analyzer.morphology_types.get(morph_id, morph_id)
                print(f"     {morph_id} ({morph_name})")

        if category_count > 0:
            print(f"\n   🏪 POI类别 (前10):")
            poi_categories = sorted(analyzer.entity_types['Category'])[:10]
            for category_id in poi_categories:
                category_name = analyzer.poi_categories.get(category_id, category_id)
                print(f"     {category_id} ({category_name})")
            if len(analyzer.entity_types['Category']) > 10:
                print(f"     ... 还有 {len(analyzer.entity_types['Category']) - 10} 个类别")

        # 连通性总结
        entity_connections = defaultdict(int)
        for head, relation, tail in analyzer.triples:
            entity_connections[head] += 1
            entity_connections[tail] += 1

        avg_degree = sum(entity_connections.values()) / len(analyzer.entities) if analyzer.entities else 0
        max_degree = max(entity_connections.values()) if entity_connections else 0

        print(f"\n📊 连通性总结:")
        print(f"   平均连接度: {avg_degree:.2f}")
        print(f"   最大连接度: {max_degree}")
        print(f"   图密度: {(2 * len(analyzer.triples)) / (len(analyzer.entities) * (len(analyzer.entities) - 1)):.6f}" if len(analyzer.entities) > 1 else "   图密度: 0")

        print(f"\n🎉 分析完成！请查看输出目录: {output_dir}")
        print("   📊 enhanced_kg_analysis_visualization.png - 增强版可视化图表")
        print("   📝 kg_analysis_report_*.txt - 详细分析报告")
        print("   📄 *.json - 各类详细分析数据")
    else:
        print("\n❌ 分析过程中出现错误，请检查文件路径和格式")


if __name__ == "__main__":
    main()