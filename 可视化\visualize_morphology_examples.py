"""
形态类别地块可视化展示
展示每个形态类别的代表性地块图像
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np

def find_block_images(block_id, image_dirs):
    """
    查找地块对应的图像文件
    """
    # 提取地块编号
    if block_id.startswith('Land_'):
        block_num = block_id[5:]  # 去掉 'Land_' 前缀
    else:
        block_num = block_id
    
    # 在不同目录中查找对应的图像
    for img_dir in image_dirs:
        if os.path.exists(img_dir):
            # 查找可能的文件名格式
            possible_names = [
                f"{block_num}.png",
                f"{block_id}.png",
                f"{int(block_num)}.png" if block_num.isdigit() else None
            ]
            
            for name in possible_names:
                if name:
                    img_path = os.path.join(img_dir, name)
                    if os.path.exists(img_path):
                        return img_path
    
    return None

def create_morphology_showcase():
    """
    创建形态类别展示图
    """
    print("🎨 创建形态类别展示图...")
    
    # 读取分析结果
    df = pd.read_csv('morphology_blocks_analysis.csv')
    
    # 图像目录
    image_dirs = ['out', 'outdikuai']
    
    # 创建大图
    fig, axes = plt.subplots(11, 4, figsize=(16, 44))
    fig.suptitle('城市形态类别地块展示\nUrban Morphology Block Examples', fontsize=20, fontweight='bold')
    
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    for row_idx, (_, row) in enumerate(df.iterrows()):
        morph_name = row['形态类别名称']
        morph_id = row['形态类别ID']
        count = row['地块数量']
        
        # 获取示例地块
        example_blocks = [
            row['示例地块1'], row['示例地块2'], 
            row['示例地块3'], row['示例地块4']
        ]
        
        for col_idx, block_id in enumerate(example_blocks):
            ax = axes[row_idx, col_idx]
            
            if pd.isna(block_id) or block_id == '':
                ax.text(0.5, 0.5, '无图像', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=12)
                ax.set_xticks([])
                ax.set_yticks([])
                continue
            
            # 查找图像文件
            img_path = find_block_images(block_id, image_dirs)
            
            if img_path and os.path.exists(img_path):
                try:
                    # 加载并显示图像
                    img = Image.open(img_path)
                    ax.imshow(img)
                    ax.set_title(f'{block_id}', fontsize=10)
                    ax.set_xticks([])
                    ax.set_yticks([])
                except Exception as e:
                    ax.text(0.5, 0.5, f'加载失败\n{block_id}', ha='center', va='center',
                           transform=ax.transAxes, fontsize=10)
                    ax.set_xticks([])
                    ax.set_yticks([])
            else:
                ax.text(0.5, 0.5, f'未找到图像\n{block_id}', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10)
                ax.set_xticks([])
                ax.set_yticks([])
        
        # 在第一列添加类别标签
        axes[row_idx, 0].text(-0.3, 0.5, f'{morph_name}\n({count}个地块)', 
                             rotation=90, ha='center', va='center',
                             transform=axes[row_idx, 0].transAxes, 
                             fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('morphology_showcase.png', dpi=300, bbox_inches='tight')
    plt.savefig('morphology_showcase.pdf', bbox_inches='tight')
    print("✅ 展示图已保存: morphology_showcase.png 和 morphology_showcase.pdf")
    
    return fig

def create_summary_report():
    """
    创建汇总报告
    """
    print("\n📊 创建汇总报告...")
    
    df = pd.read_csv('morphology_blocks_analysis.csv')
    
    print("\n" + "="*80)
    print("🏙️ 城市形态类别地块详情汇总")
    print("="*80)
    
    total_blocks = df['地块数量'].sum()
    
    for _, row in df.iterrows():
        morph_name = row['形态类别名称']
        count = row['地块数量']
        percentage = (count / total_blocks) * 100
        
        examples = [row['示例地块1'], row['示例地块2'], 
                   row['示例地块3'], row['示例地块4']]
        examples = [ex for ex in examples if pd.notna(ex) and ex != '']
        
        print(f"\n🏗️ {morph_name}")
        print(f"   数量: {count:4d} 个地块 ({percentage:5.1f}%)")
        print(f"   示例: {', '.join(examples[:4])}")
        
        # 显示更多示例（如果有的话）
        if len(examples) >= 4:
            print(f"   代表性地块: {examples[0]}, {examples[1]}, {examples[2]}, {examples[3]}")
    
    print(f"\n📈 总计: {total_blocks} 个地块")
    print("="*80)

def main():
    """主函数"""
    print("🚀 启动形态类别可视化展示")
    
    # 检查必要文件
    if not os.path.exists('morphology_blocks_analysis.csv'):
        print("❌ 请先运行 morphology_analysis.py 生成分析结果")
        return
    
    # 检查图像目录
    image_dirs = ['out', 'outdikuai']
    available_dirs = [d for d in image_dirs if os.path.exists(d)]
    
    if not available_dirs:
        print("❌ 未找到图像目录 (out 或 outdikuai)")
        return
    
    print(f"📁 找到图像目录: {', '.join(available_dirs)}")
    
    try:
        # 创建汇总报告
        create_summary_report()
        
        # 创建可视化展示
        fig = create_morphology_showcase()
        
        print("\n✅ 可视化展示完成!")
        print("📄 查看文件:")
        print("   - morphology_showcase.png (展示图)")
        print("   - morphology_showcase.pdf (PDF版本)")
        print("   - morphology_blocks_analysis.csv (详细数据)")
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
