"""
形态类别地块分析器
分析知识图谱中每个形态类别对应的具体地块
"""

import pandas as pd
from collections import defaultdict
import random

def analyze_morphology_blocks(kg_file_path):
    """
    分析每个形态类别对应的地块
    """
    print("🔍 分析形态类别对应的地块...")
    
    # 形态类型映射
    morphology_types = {
        'Morph_LowRiseLowDensity': '低层低密度',
        'Morph_LowRiseMidDensity': '低层中密度', 
        'Morph_LowRiseHighDensity': '低层高密度',
        'Morph_MidRiseLowDensity': '中层低密度',
        'Morph_MidRiseMidDensity': '中层中密度',
        'Morph_MidRiseHighDensity': '中层高密度',
        'Morph_HighRiseLowDensity': '高层低密度',
        'Morph_HighRiseMidDensity': '高层中密度',
        'Morph_HighRiseHighDensity': '高层高密度',
        'Morph_Vacant': '空地',
        'Morph_SuperHighRise': '超高层'
    }
    
    # 存储每个形态类别对应的地块
    morphology_blocks = defaultdict(list)
    
    # 读取知识图谱文件
    with open(kg_file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('\t')
            if len(parts) != 3:
                continue
                
            head, relation, tail = parts
            
            # 查找形态关系
            if relation == 'hasMorphology' and head.startswith('Land_') and tail.startswith('Morph_'):
                morphology_blocks[tail].append(head)
    
    print(f"✅ 分析完成，发现 {len(morphology_blocks)} 种形态类别")
    
    # 按统计数据中的顺序显示结果
    morphology_order = [
        'Morph_MidRiseMidDensity',
        'Morph_Vacant', 
        'Morph_MidRiseHighDensity',
        'Morph_HighRiseLowDensity',
        'Morph_MidRiseLowDensity',
        'Morph_LowRiseMidDensity',
        'Morph_HighRiseMidDensity',
        'Morph_LowRiseLowDensity',
        'Morph_LowRiseHighDensity',
        'Morph_HighRiseHighDensity',
        'Morph_SuperHighRise'
    ]
    
    print("\n" + "="*80)
    print("📊 各形态类别对应的地块详情")
    print("="*80)
    
    for morph_id in morphology_order:
        if morph_id in morphology_blocks:
            blocks = morphology_blocks[morph_id]
            morph_name = morphology_types.get(morph_id, morph_id)
            count = len(blocks)
            
            print(f"\n🏗️ {morph_id} ({morph_name}): {count} 个地块")
            print("-" * 60)
            
            # 显示前10个地块作为示例
            sample_blocks = blocks[:10] if len(blocks) >= 10 else blocks
            
            print("示例地块:")
            for i, block in enumerate(sample_blocks, 1):
                print(f"  {i:2d}. {block}")
            
            if len(blocks) > 10:
                print(f"  ... 还有 {len(blocks) - 10} 个地块")
            
            # 如果用户要求至少4个，显示更多
            if len(blocks) >= 4:
                print(f"\n前4个地块: {', '.join(blocks[:4])}")
            
            print(f"总计: {count} 个地块")
    
    # 生成详细报告
    generate_detailed_report(morphology_blocks, morphology_types)
    
    return morphology_blocks

def generate_detailed_report(morphology_blocks, morphology_types):
    """生成详细的形态类别报告"""
    print("\n" + "="*80)
    print("📝 生成详细报告...")
    
    # 创建DataFrame用于分析
    data = []
    for morph_id, blocks in morphology_blocks.items():
        morph_name = morphology_types.get(morph_id, morph_id)
        count = len(blocks)
        
        # 随机选择4个地块作为示例
        sample_blocks = random.sample(blocks, min(4, len(blocks)))
        
        data.append({
            '形态类别ID': morph_id,
            '形态类别名称': morph_name,
            '地块数量': count,
            '示例地块1': sample_blocks[0] if len(sample_blocks) > 0 else '',
            '示例地块2': sample_blocks[1] if len(sample_blocks) > 1 else '',
            '示例地块3': sample_blocks[2] if len(sample_blocks) > 2 else '',
            '示例地块4': sample_blocks[3] if len(sample_blocks) > 3 else '',
        })
    
    df = pd.DataFrame(data)
    
    # 按地块数量排序
    df = df.sort_values('地块数量', ascending=False)
    
    # 保存到CSV文件
    df.to_csv('morphology_blocks_analysis.csv', index=False, encoding='utf-8-sig')
    print("✅ 详细报告已保存到: morphology_blocks_analysis.csv")
    
    # 显示汇总表格
    print("\n📋 形态类别汇总表:")
    print(df.to_string(index=False))

def main():
    """主函数"""
    kg_file_path = "kg_with_building_fixed.txt"
    
    print("🚀 启动形态类别地块分析器")
    print(f"📁 分析文件: {kg_file_path}")
    
    try:
        morphology_blocks = analyze_morphology_blocks(kg_file_path)
        
        print(f"\n✅ 分析完成!")
        print(f"📊 发现 {len(morphology_blocks)} 种形态类别")
        print(f"📄 详细报告已保存")
        
    except FileNotFoundError:
        print(f"❌ 文件未找到: {kg_file_path}")
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
