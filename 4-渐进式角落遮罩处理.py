import os
import numpy as np
import geopandas as gpd
import rasterio
from rasterio.mask import mask
from rasterio.plot import reshape_as_image
import matplotlib.pyplot as plt
from shapely.geometry import box, mapping, Point
import cv2

def process_satellite_images(tif_path, shp_path, output_dir,
                             mask_threshold=0.8,
                             min_corner_size=10,
                             max_corner_size=100,
                             step_size=5):
    """
    处理卫星图像，为每个街区生成独立的PNG图像，目标街区居中显示
    注意：黑色遮罩功能已禁用，将输出完整的原始图像

    参数:
    tif_path: 卫星图像TIF文件路径
    shp_path: 街区数据SHP文件路径
    output_dir: 输出目录路径
    mask_threshold: 角落遮罩阈值（已禁用）
    min_corner_size: 角落遮罩的最小起始尺寸（已禁用）
    max_corner_size: 角落遮罩的最大尺寸（已禁用）
    step_size: 角落遮罩尺寸的增长步长（已禁用）
    """
    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 加载街区数据
    blocks = gpd.read_file(shp_path)
    print(f"加载了 {len(blocks)} 个街区")

    if len(blocks) == 0:
        print("警告：街区数据为空！")
        return

    # 确保街区数据有BlockID字段
    if 'BlockID' not in blocks.columns:
        # 如果没有BlockID字段，使用索引作为ID
        blocks['BlockID'] = blocks.index.astype(str)
        print("警告：未找到'BlockID'字段，使用索引作为替代")

    # 打开卫星图像
    try:
        with rasterio.open(tif_path) as src:
            # 获取图像数据和元数据
            satellite_img = src.read()
            full_img = reshape_as_image(satellite_img)
            print(f"卫星图像尺寸: {full_img.shape}")
            tif_meta = src.meta
            tif_bounds = src.bounds
            tif_crs = src.crs

            # 确保街区数据与卫星图像使用相同的坐标参考系统
            if blocks.crs != tif_crs:
                blocks = blocks.to_crs(tif_crs)

            # 创建整个卫星图像的边界框
            tif_bbox = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)

            # 处理每个街区
            processed_count = 0
            skipped_count = 0

            # 先尝试保存一个测试图像，验证保存功能是否正常
            test_img = np.zeros((224, 224, 3), dtype=np.uint8)  # 创建一个黑色测试图像
            test_path = os.path.join(output_dir, "test_image.png")
            try:
                cv2.imwrite(test_path, test_img)
                print(f"测试图像保存成功：{test_path}")
            except Exception as e:
                print(f"保存测试图像失败：{e}")

            for idx, block in blocks.iterrows():
                block_id = str(block['BlockID'])
                print(f"处理街区 {block_id}")

                # 提取街区几何形状
                block_geom = block.geometry

                # 检查街区是否在卫星图像范围内
                if not block_geom.intersects(tif_bbox):
                    print(f"街区 {block_id} 不在卫星图像范围内，跳过")
                    skipped_count += 1
                    continue

                try:
                    # 获取街区质心作为中心点
                    centroid = block_geom.centroid

                    # 将地理坐标转换为图像像素坐标
                    py, px = src.index(centroid.x, centroid.y)

                    # 确定224x224窗口的左上角和右下角坐标
                    half_size = 112  # 224/2

                    # 计算窗口边界，确保在图像范围内
                    window_ymin = max(0, py - half_size)
                    window_ymax = min(full_img.shape[0], py + half_size)
                    window_xmin = max(0, px - half_size)
                    window_xmax = min(full_img.shape[1], px + half_size)

                    # 处理边界情况，确保窗口大小为224x224
                    if window_ymax - window_ymin < 224:
                        if window_ymin == 0:
                            window_ymax = min(full_img.shape[0], window_ymin + 224)
                        else:
                            window_ymin = max(0, window_ymax - 224)

                    if window_xmax - window_xmin < 224:
                        if window_xmin == 0:
                            window_xmax = min(full_img.shape[1], window_xmin + 224)
                        else:
                            window_xmin = max(0, window_xmax - 224)

                    # 裁剪图像
                    cropped_img = full_img[window_ymin:window_ymax, window_xmin:window_xmax]

                    # 如果裁剪后的图像尺寸不是224x224，进行调整
                    if cropped_img.shape[0] != 224 or cropped_img.shape[1] != 224:
                        cropped_img = cv2.resize(cropped_img, (224, 224), interpolation=cv2.INTER_AREA)

                    # 确保cropped_img是一个有效的图像
                    if cropped_img.size == 0 or np.all(cropped_img == 0):
                        print(f"警告：街区 {block_id} 的裁剪图像为空或全黑")
                        skipped_count += 1
                        continue

                    print(f"裁剪图像形状: {cropped_img.shape}, 类型: {cropped_img.dtype}")

                    # 角落遮罩处理（已禁用，直接返回原图）
                    processed_img = apply_corner_masks(cropped_img,
                                                     threshold=mask_threshold,
                                                     min_corner_size=min_corner_size,
                                                     max_corner_size=max_corner_size,
                                                     step_size=step_size)

                    # 保存图像前先检查
                    print(f"处理后图像形状: {processed_img.shape}, 类型: {processed_img.dtype}")
                    print(f"图像值范围: 最小={np.min(processed_img)}, 最大={np.max(processed_img)}")

                    # 确保图像是uint8类型
                    if processed_img.dtype != np.uint8:
                        processed_img = processed_img.astype(np.uint8)

                    # 直接保存图像到输出目录，不创建子文件夹
                    output_path = os.path.join(output_dir, f"{block_id}.png")

                    # 处理RGBA图像保存问题
                    try:
                        # 如果是4通道图像，需要特殊处理
                        if processed_img.shape[2] == 4:
                            print("检测到4通道图像，进行特殊处理")
                            # 方法1：转换为3通道再保存
                            bgr_img = cv2.cvtColor(processed_img[:,:,0:3], cv2.COLOR_RGB2BGR)
                            success = cv2.imwrite(output_path, bgr_img)
                            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                                raise Exception("转换为3通道保存失败")
                        else:
                            # 正常保存3通道图像
                            bgr_img = cv2.cvtColor(processed_img, cv2.COLOR_RGB2BGR)
                            success = cv2.imwrite(output_path, bgr_img)
                            if not success:
                                raise Exception("OpenCV保存失败")

                        print(f"成功保存图像到: {output_path}")

                        # 验证文件创建
                        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                            print(f"验证成功: 文件大小 {os.path.getsize(output_path)} 字节")
                            processed_count += 1
                        else:
                            print(f"验证失败: 文件不存在或大小为0")

                    except Exception as e:
                        print(f"保存图像失败: {e}")
                        # 尝试备用保存方法
                        try:
                            from PIL import Image
                            # 转换为RGB模式（去掉Alpha通道）
                            if processed_img.shape[2] == 4:
                                rgb_img = processed_img[:,:,0:3]
                            else:
                                rgb_img = processed_img

                            pil_img = Image.fromarray(rgb_img)
                            pil_img.save(output_path)
                            print(f"使用PIL成功保存图像: {output_path}")

                            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                                processed_count += 1
                        except Exception as e2:
                            print(f"PIL保存也失败: {e2}")

                except Exception as e:
                    print(f"处理街区 {block_id} 时出错: {e}")
                    import traceback
                    traceback.print_exc()

            print(f"统计信息: 处理成功 {processed_count}/{len(blocks)} 个街区, 跳过 {skipped_count} 个街区")

    except Exception as e:
        print(f"打开卫星图像时发生错误: {e}")
        import traceback
        traceback.print_exc()

def apply_corner_masks(img, threshold=0.8, min_corner_size=10, max_corner_size=100, step_size=5):
    """
    角落遮罩处理函数 - 已禁用黑色遮罩功能

    参数:
    img: 输入图像
    threshold: 阈值（已禁用）
    min_corner_size: 角落遮罩的最小起始尺寸（已禁用）
    max_corner_size: 角落遮罩的最大尺寸（已禁用）
    step_size: 遮罩每次增长的像素数（已禁用）

    返回:
    原始图像（未应用任何遮罩）
    """
    # 直接返回原图像，不应用任何黑色遮罩
    result = img.copy()

    # 确保图像是uint8类型
    if result.dtype != np.uint8:
        result = result.astype(np.uint8)

    print(f"输入图像形状: {img.shape}, 黑色遮罩已禁用，返回原始图像")

    return result

def main():
    # 设置输入和输出路径
    tif_path = r"C:\Users\<USER>\Desktop\22\11-卫星数据\影像下载_2503152313.tif"
    shp_path = r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L5.shp"
    output_dir = r"D:\研二\知识图谱\图例\形态分类\outdikuai"

    # 添加文件存在检查
    if not os.path.exists(tif_path):
        print(f"错误：卫星图像文件不存在: {tif_path}")
        return

    if not os.path.exists(shp_path):
        print(f"错误：街区文件不存在: {shp_path}")
        return

    # 测试输出目录写入权限
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        test_file = os.path.join(output_dir, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("测试写入权限")
        os.remove(test_file)
        print(f"输出目录测试正常: {output_dir}")
    except Exception as e:
        print(f"输出目录写入错误: {e}")
        return

    # 角落遮罩参数设置
    mask_params = {
        "mask_threshold": 0.9,     # 阈值越低，遮罩越多
        "min_corner_size": 60,      # 最小遮罩尺寸
        "max_corner_size": 112,     # 最大遮罩尺寸
        "step_size": 10              # 遮罩增长步长
    }

    # 处理卫星图像
    process_satellite_images(tif_path, shp_path, output_dir, **mask_params)

if __name__ == "__main__":
    main()

'''
处理流程示例：
-------------

1. 数据输入
   - 卫星图像(TIF): 包含整个城市区域的高分辨率遥感图像
   - 街区边界(SHP): 包含每个街区ID和几何边界的矢量文件

2. 预处理步骤
   - 加载卫星图像和街区边界数据
   - 确保坐标系统一致
   - 确认每个街区有唯一ID

3. 街区提取流程（每个街区）
   - 计算街区的中心点(质心)
   - 将地理坐标转换为图像像素坐标
   - 以街区中心为中心，提取224*224大小的卫星图像窗口
   - 处理图像边缘情况，确保输出图像始终为224*224像素

4. 角落区域处理（已禁用）
   - 原本用于计算中心区域和四个角落区域的特征密度
   - 原本当角落区域特征密度超过中心区域的0.8阈值时添加黑色遮罩
   - 现在直接输出原始图像，不添加任何遮罩

5. 输出结果
   - 每个街区生成一张224*224像素的PNG图像
   - 目标街区位于图像中心
   - 周围区域显示其他街区和自然区域
   - 输出完整的原始图像，不添加任何黑色遮罩

6. 应用价值
   - 保留目标街区周围的完整环境上下文信息
   - 提供原始的、未经遮罩处理的卫星图像数据
   - 统一的图像尺寸便于深度学习模型处理
   - 保持图像的完整性和真实性

处理前后对比示例：
原始卫星图像：包含整个城市的大图像，可能是几千乃至上万像素
处理后图像：224*224像素，目标街区位于中心，保留完整的原始图像内容
'''