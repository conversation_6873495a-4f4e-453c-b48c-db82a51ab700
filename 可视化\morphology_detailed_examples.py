"""
详细展示每个形态类别的地块示例
"""

import pandas as pd
from collections import defaultdict

def get_detailed_examples():
    """获取每个形态类别的详细地块示例"""
    
    # 从知识图谱中重新提取更多地块示例
    morphology_blocks = defaultdict(list)
    
    print("🔍 从知识图谱中提取地块信息...")
    
    with open('kg_with_building_fixed.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('\t')
            if len(parts) != 3:
                continue
                
            head, relation, tail = parts
            
            if relation == 'hasMorphology' and head.startswith('Land_') and tail.startswith('Morph_'):
                morphology_blocks[tail].append(head)
    
    # 形态类型信息
    morphology_info = {
        'Morph_MidRiseMidDensity': {
            'name': '中层中密度',
            'description': '中等高度建筑，中等密度开发',
            'count': 935,
            'percentage': 42.8
        },
        'Morph_Vacant': {
            'name': '空地',
            'description': '未开发或空置的土地',
            'count': 356,
            'percentage': 16.3
        },
        'Morph_MidRiseHighDensity': {
            'name': '中层高密度',
            'description': '中等高度建筑，高密度开发',
            'count': 273,
            'percentage': 12.5
        },
        'Morph_HighRiseLowDensity': {
            'name': '高层低密度',
            'description': '高层建筑，低密度开发',
            'count': 237,
            'percentage': 10.8
        },
        'Morph_MidRiseLowDensity': {
            'name': '中层低密度',
            'description': '中等高度建筑，低密度开发',
            'count': 220,
            'percentage': 10.1
        },
        'Morph_LowRiseMidDensity': {
            'name': '低层中密度',
            'description': '低层建筑，中等密度开发',
            'count': 70,
            'percentage': 3.2
        },
        'Morph_HighRiseMidDensity': {
            'name': '高层中密度',
            'description': '高层建筑，中等密度开发',
            'count': 40,
            'percentage': 1.8
        },
        'Morph_LowRiseLowDensity': {
            'name': '低层低密度',
            'description': '低层建筑，低密度开发',
            'count': 28,
            'percentage': 1.3
        },
        'Morph_LowRiseHighDensity': {
            'name': '低层高密度',
            'description': '低层建筑，高密度开发',
            'count': 20,
            'percentage': 0.9
        },
        'Morph_HighRiseHighDensity': {
            'name': '高层高密度',
            'description': '高层建筑，高密度开发',
            'count': 4,
            'percentage': 0.2
        },
        'Morph_SuperHighRise': {
            'name': '超高层',
            'description': '超高层建筑',
            'count': 4,
            'percentage': 0.2
        }
    }
    
    print("\n" + "="*100)
    print("🏙️ 城市形态类别详细地块展示")
    print("="*100)
    
    for morph_id, info in morphology_info.items():
        if morph_id in morphology_blocks:
            blocks = morphology_blocks[morph_id]
            
            print(f"\n🏗️ {info['name']} ({morph_id})")
            print(f"   📊 统计: {info['count']} 个地块 ({info['percentage']}%)")
            print(f"   📝 描述: {info['description']}")
            print(f"   🏠 地块示例:")
            
            # 显示前8个地块作为示例
            examples = blocks[:8] if len(blocks) >= 8 else blocks
            
            for i, block in enumerate(examples, 1):
                print(f"      {i:2d}. {block}")
            
            if len(blocks) > 8:
                print(f"      ... 还有 {len(blocks) - 8} 个地块")
            
            print(f"   📍 代表性地块: {', '.join(blocks[:4])}")
            print("-" * 80)
    
    return morphology_blocks, morphology_info

def create_markdown_report(morphology_blocks, morphology_info):
    """创建Markdown格式的报告"""
    
    print("\n📝 生成Markdown报告...")
    
    markdown_content = """# 城市形态类别地块详情报告

## 概述
本报告展示了城市中不同形态类别的地块分布情况，共包含11种形态类别，总计2187个地块。

## 各形态类别详情

"""
    
    for morph_id, info in morphology_info.items():
        if morph_id in morphology_blocks:
            blocks = morphology_blocks[morph_id]
            
            markdown_content += f"""### {info['name']} ({morph_id})

- **数量**: {info['count']} 个地块 ({info['percentage']}%)
- **描述**: {info['description']}
- **代表性地块**: {', '.join(blocks[:4])}

**地块示例**:
"""
            
            examples = blocks[:8] if len(blocks) >= 8 else blocks
            for i, block in enumerate(examples, 1):
                markdown_content += f"{i}. {block}\n"
            
            if len(blocks) > 8:
                markdown_content += f"... 还有 {len(blocks) - 8} 个地块\n"
            
            markdown_content += "\n---\n\n"
    
    markdown_content += """## 统计汇总

| 形态类别 | 地块数量 | 占比 | 代表性地块 |
|---------|---------|------|-----------|
"""
    
    for morph_id, info in morphology_info.items():
        if morph_id in morphology_blocks:
            blocks = morphology_blocks[morph_id]
            examples = ', '.join(blocks[:2])
            markdown_content += f"| {info['name']} | {info['count']} | {info['percentage']}% | {examples} |\n"
    
    # 保存Markdown文件
    with open('morphology_detailed_report.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print("✅ Markdown报告已保存: morphology_detailed_report.md")

def main():
    """主函数"""
    print("🚀 启动详细形态类别地块展示")
    
    try:
        morphology_blocks, morphology_info = get_detailed_examples()
        create_markdown_report(morphology_blocks, morphology_info)
        
        print(f"\n✅ 详细展示完成!")
        print(f"📊 总共分析了 {len(morphology_blocks)} 种形态类别")
        print(f"📄 生成的文件:")
        print(f"   - morphology_detailed_report.md (详细报告)")
        
    except Exception as e:
        print(f"❌ 展示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
